Action Log - 2025-06-14 15:02:17
================================================================================

[[15:02:16]] [INFO] Generating execution report...
[[15:02:16]] [SUCCESS] Screenshot refreshed
[[15:02:16]] [INFO] Refreshing screenshot...
[[15:02:15]] [SUCCESS] Screenshot refreshed successfully
[[15:02:15]] [SUCCESS] Screenshot refreshed successfully
[[15:02:14]] [INFO] Executing action 25/25: Add Log: App is closed (with screenshot)
[[15:02:14]] [SUCCESS] Screenshot refreshed
[[15:02:14]] [INFO] Refreshing screenshot...
[[15:02:13]] [SUCCESS] Screenshot refreshed
[[15:02:13]] [INFO] Refreshing screenshot...
[[15:02:12]] [SUCCESS] Screenshot refreshed successfully
[[15:02:12]] [SUCCESS] Screenshot refreshed successfully
[[15:02:11]] [INFO] Executing Multi Step action step 9/9: Add Log: App is closed (with screenshot)
[[15:02:10]] [SUCCESS] Screenshot refreshed
[[15:02:10]] [INFO] Refreshing screenshot...
[[15:01:56]] [SUCCESS] Screenshot refreshed successfully
[[15:01:56]] [SUCCESS] Screenshot refreshed successfully
[[15:01:55]] [INFO] Executing Multi Step action step 8/9: Execute Test Case: apple health (8 steps)
[[15:01:55]] [SUCCESS] Screenshot refreshed
[[15:01:55]] [INFO] Refreshing screenshot...
[[15:01:52]] [SUCCESS] Screenshot refreshed successfully
[[15:01:52]] [SUCCESS] Screenshot refreshed successfully
[[15:01:52]] [INFO] Executing Multi Step action step 7/9: Terminate app: com.apple.Health
[[15:01:51]] [SUCCESS] Screenshot refreshed
[[15:01:51]] [INFO] Refreshing screenshot...
[[15:01:47]] [SUCCESS] Screenshot refreshed successfully
[[15:01:47]] [SUCCESS] Screenshot refreshed successfully
[[15:01:47]] [INFO] Executing Multi Step action step 6/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[15:01:46]] [SUCCESS] Screenshot refreshed
[[15:01:46]] [INFO] Refreshing screenshot...
[[15:01:43]] [SUCCESS] Screenshot refreshed successfully
[[15:01:43]] [SUCCESS] Screenshot refreshed successfully
[[15:01:43]] [INFO] Executing Multi Step action step 5/9: Add Log: Clicked on Edit link successfully (with screenshot)
[[15:01:42]] [SUCCESS] Screenshot refreshed
[[15:01:42]] [INFO] Refreshing screenshot...
[[15:01:38]] [SUCCESS] Screenshot refreshed successfully
[[15:01:38]] [SUCCESS] Screenshot refreshed successfully
[[15:01:38]] [INFO] Executing Multi Step action step 4/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[15:01:37]] [SUCCESS] Screenshot refreshed
[[15:01:37]] [INFO] Refreshing screenshot...
[[15:01:34]] [SUCCESS] Screenshot refreshed successfully
[[15:01:34]] [SUCCESS] Screenshot refreshed successfully
[[15:01:33]] [INFO] Executing Multi Step action step 3/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[15:01:33]] [SUCCESS] Screenshot refreshed
[[15:01:33]] [INFO] Refreshing screenshot...
[[15:01:30]] [SUCCESS] Screenshot refreshed successfully
[[15:01:30]] [SUCCESS] Screenshot refreshed successfully
[[15:01:29]] [INFO] Executing Multi Step action step 2/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[15:01:29]] [SUCCESS] Screenshot refreshed
[[15:01:29]] [INFO] Refreshing screenshot...
[[15:01:24]] [SUCCESS] Screenshot refreshed successfully
[[15:01:24]] [SUCCESS] Screenshot refreshed successfully
[[15:01:23]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[15:01:23]] [INFO] Loaded 9 steps from test case: apple health
[[15:01:23]] [INFO] Loading steps for Multi Step action: apple health
[[15:01:23]] [INFO] Executing action 24/25: Execute Test Case: apple health (8 steps)
[[15:01:23]] [SUCCESS] Screenshot refreshed
[[15:01:23]] [INFO] Refreshing screenshot...
[[15:01:20]] [SUCCESS] Screenshot refreshed successfully
[[15:01:20]] [SUCCESS] Screenshot refreshed successfully
[[15:01:20]] [INFO] Executing action 23/25: Terminate app: com.apple.Health
[[15:01:19]] [SUCCESS] Screenshot refreshed
[[15:01:19]] [INFO] Refreshing screenshot...
[[15:01:16]] [SUCCESS] Screenshot refreshed successfully
[[15:01:16]] [SUCCESS] Screenshot refreshed successfully
[[15:01:15]] [INFO] Executing action 22/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[15:01:15]] [SUCCESS] Screenshot refreshed
[[15:01:15]] [INFO] Refreshing screenshot...
[[15:01:13]] [SUCCESS] Screenshot refreshed successfully
[[15:01:13]] [SUCCESS] Screenshot refreshed successfully
[[15:01:13]] [INFO] Executing action 21/25: Add Log: Clicked on Edit link successfully (with screenshot)
[[15:01:12]] [SUCCESS] Screenshot refreshed
[[15:01:12]] [INFO] Refreshing screenshot...
[[15:01:09]] [SUCCESS] Screenshot refreshed successfully
[[15:01:09]] [SUCCESS] Screenshot refreshed successfully
[[15:01:08]] [INFO] Executing action 20/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[15:01:08]] [SUCCESS] Screenshot refreshed
[[15:01:08]] [INFO] Refreshing screenshot...
[[15:01:05]] [SUCCESS] Screenshot refreshed successfully
[[15:01:05]] [SUCCESS] Screenshot refreshed successfully
[[15:01:04]] [INFO] Executing action 19/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[15:01:04]] [SUCCESS] Screenshot refreshed
[[15:01:04]] [INFO] Refreshing screenshot...
[[15:01:01]] [SUCCESS] Screenshot refreshed successfully
[[15:01:01]] [SUCCESS] Screenshot refreshed successfully
[[15:01:00]] [INFO] Executing action 18/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[15:00:59]] [SUCCESS] Screenshot refreshed
[[15:00:59]] [INFO] Refreshing screenshot...
[[15:00:47]] [SUCCESS] Screenshot refreshed successfully
[[15:00:47]] [SUCCESS] Screenshot refreshed successfully
[[15:00:46]] [INFO] Executing action 17/25: Launch app: com.apple.Health
[[15:00:46]] [SUCCESS] Screenshot refreshed
[[15:00:46]] [INFO] Refreshing screenshot...
[[15:00:46]] [INFO] Skipping Hook Action during normal execution. This action will only be executed when a step fails.
[[15:00:46]] [INFO] Executing action 16/25: Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"] (Recovery)
[[15:00:46]] [SUCCESS] Screenshot refreshed
[[15:00:46]] [INFO] Refreshing screenshot...
[[15:00:45]] [SUCCESS] Screenshot refreshed
[[15:00:45]] [INFO] Refreshing screenshot...
[[15:00:44]] [SUCCESS] Screenshot refreshed successfully
[[15:00:44]] [SUCCESS] Screenshot refreshed successfully
[[15:00:43]] [INFO] Executing Multi Step action step 9/9: Add Log: App is closed (with screenshot)
[[15:00:42]] [SUCCESS] Screenshot refreshed
[[15:00:42]] [INFO] Refreshing screenshot...
[[15:00:40]] [SUCCESS] Screenshot refreshed successfully
[[15:00:40]] [SUCCESS] Screenshot refreshed successfully
[[15:00:39]] [INFO] Executing Multi Step action step 8/9: Terminate app: com.apple.Health
[[15:00:39]] [SUCCESS] Screenshot refreshed
[[15:00:39]] [INFO] Refreshing screenshot...
[[15:00:36]] [SUCCESS] Screenshot refreshed successfully
[[15:00:36]] [SUCCESS] Screenshot refreshed successfully
[[15:00:36]] [INFO] Executing Multi Step action step 7/9: Wait for 1 ms
[[15:00:35]] [SUCCESS] Screenshot refreshed
[[15:00:35]] [INFO] Refreshing screenshot...
[[15:00:34]] [SUCCESS] Screenshot refreshed successfully
[[15:00:34]] [SUCCESS] Screenshot refreshed successfully
[[15:00:33]] [INFO] Executing Multi Step action step 6/9: Add Log: Done link is clicked (with screenshot)
[[15:00:33]] [SUCCESS] Screenshot refreshed
[[15:00:33]] [INFO] Refreshing screenshot...
[[15:00:29]] [SUCCESS] Screenshot refreshed successfully
[[15:00:29]] [SUCCESS] Screenshot refreshed successfully
[[15:00:29]] [INFO] Executing Multi Step action step 5/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[15:00:28]] [SUCCESS] Screenshot refreshed
[[15:00:28]] [INFO] Refreshing screenshot...
[[15:00:27]] [SUCCESS] Screenshot refreshed successfully
[[15:00:27]] [SUCCESS] Screenshot refreshed successfully
[[15:00:26]] [INFO] Executing Multi Step action step 4/9: Add Log: Edit link is clicked (with screenshot)
[[15:00:25]] [SUCCESS] Screenshot refreshed
[[15:00:25]] [INFO] Refreshing screenshot...
[[15:00:23]] [SUCCESS] Screenshot refreshed successfully
[[15:00:23]] [SUCCESS] Screenshot refreshed successfully
[[15:00:22]] [INFO] Executing Multi Step action step 3/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[15:00:21]] [SUCCESS] Screenshot refreshed
[[15:00:21]] [INFO] Refreshing screenshot...
[[15:00:20]] [SUCCESS] Screenshot refreshed successfully
[[15:00:20]] [SUCCESS] Screenshot refreshed successfully
[[15:00:19]] [INFO] Executing Multi Step action step 2/9: Add Log: Launched App Successfully (with screenshot)
[[15:00:19]] [SUCCESS] Screenshot refreshed
[[15:00:19]] [INFO] Refreshing screenshot...
[[15:00:14]] [SUCCESS] Screenshot refreshed successfully
[[15:00:14]] [SUCCESS] Screenshot refreshed successfully
[[15:00:13]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[15:00:13]] [INFO] Loaded 9 steps from test case: health2
[[15:00:13]] [INFO] Loading steps for Multi Step action: health2
[[15:00:13]] [INFO] Executing action 15/25: Execute Test Case: health2 (9 steps)
[[15:00:13]] [SUCCESS] Screenshot refreshed
[[15:00:13]] [INFO] Refreshing screenshot...
[[15:00:11]] [SUCCESS] Screenshot refreshed successfully
[[15:00:11]] [SUCCESS] Screenshot refreshed successfully
[[15:00:11]] [INFO] Executing action 14/25: Add Log: Closed App Successfully (with screenshot)
[[15:00:10]] [SUCCESS] Screenshot refreshed
[[15:00:10]] [INFO] Refreshing screenshot...
[[15:00:08]] [SUCCESS] Screenshot refreshed successfully
[[15:00:08]] [SUCCESS] Screenshot refreshed successfully
[[15:00:07]] [INFO] Executing action 13/25: Terminate app: com.apple.Health
[[15:00:07]] [SUCCESS] Screenshot refreshed
[[15:00:07]] [INFO] Refreshing screenshot...
[[15:00:03]] [SUCCESS] Screenshot refreshed successfully
[[15:00:03]] [SUCCESS] Screenshot refreshed successfully
[[15:00:03]] [INFO] Executing action 12/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[15:00:02]] [SUCCESS] Screenshot refreshed
[[15:00:02]] [INFO] Refreshing screenshot...
[[15:00:00]] [SUCCESS] Screenshot refreshed successfully
[[15:00:00]] [SUCCESS] Screenshot refreshed successfully
[[14:59:59]] [INFO] Executing action 11/25: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"]
[[14:59:58]] [SUCCESS] Screenshot refreshed
[[14:59:58]] [INFO] Refreshing screenshot...
[[14:59:46]] [SUCCESS] Screenshot refreshed successfully
[[14:59:46]] [SUCCESS] Screenshot refreshed successfully
[[14:59:45]] [INFO] Executing action 10/25: Launch app: com.apple.Health
[[14:59:45]] [SUCCESS] Screenshot refreshed
[[14:59:45]] [INFO] Refreshing screenshot...
[[14:59:43]] [SUCCESS] Screenshot refreshed successfully
[[14:59:43]] [SUCCESS] Screenshot refreshed successfully
[[14:59:42]] [INFO] Executing action 9/25: Add Log: App is closed (with screenshot)
[[14:59:42]] [SUCCESS] Screenshot refreshed
[[14:59:42]] [INFO] Refreshing screenshot...
[[14:59:39]] [SUCCESS] Screenshot refreshed successfully
[[14:59:39]] [SUCCESS] Screenshot refreshed successfully
[[14:59:39]] [INFO] Executing action 8/25: Terminate app: com.apple.Health
[[14:59:38]] [SUCCESS] Screenshot refreshed
[[14:59:38]] [INFO] Refreshing screenshot...
[[14:59:36]] [SUCCESS] Screenshot refreshed successfully
[[14:59:36]] [SUCCESS] Screenshot refreshed successfully
[[14:59:35]] [INFO] Executing action 7/25: Wait for 1 ms
[[14:59:35]] [SUCCESS] Screenshot refreshed
[[14:59:35]] [INFO] Refreshing screenshot...
[[14:59:33]] [SUCCESS] Screenshot refreshed successfully
[[14:59:33]] [SUCCESS] Screenshot refreshed successfully
[[14:59:32]] [INFO] Executing action 6/25: Add Log: Done link is clicked (with screenshot)
[[14:59:32]] [SUCCESS] Screenshot refreshed
[[14:59:32]] [INFO] Refreshing screenshot...
[[14:59:29]] [SUCCESS] Screenshot refreshed successfully
[[14:59:29]] [SUCCESS] Screenshot refreshed successfully
[[14:59:28]] [INFO] Executing action 5/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[14:59:27]] [SUCCESS] Screenshot refreshed
[[14:59:27]] [INFO] Refreshing screenshot...
[[14:59:26]] [SUCCESS] Screenshot refreshed successfully
[[14:59:26]] [SUCCESS] Screenshot refreshed successfully
[[14:59:25]] [INFO] Executing action 4/25: Add Log: Edit link is clicked (with screenshot)
[[14:59:25]] [SUCCESS] Screenshot refreshed
[[14:59:25]] [INFO] Refreshing screenshot...
[[14:59:22]] [SUCCESS] Screenshot refreshed successfully
[[14:59:22]] [SUCCESS] Screenshot refreshed successfully
[[14:59:21]] [INFO] Executing action 3/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[14:59:21]] [SUCCESS] Screenshot refreshed
[[14:59:21]] [INFO] Refreshing screenshot...
[[14:59:19]] [SUCCESS] Screenshot refreshed successfully
[[14:59:19]] [SUCCESS] Screenshot refreshed successfully
[[14:59:18]] [INFO] Executing action 2/25: Add Log: Launched App Successfully (with screenshot)
[[14:59:18]] [SUCCESS] Screenshot refreshed
[[14:59:18]] [INFO] Refreshing screenshot...
[[14:59:14]] [INFO] Executing action 1/25: Launch app: com.apple.Health
[[14:59:14]] [INFO] ExecutionManager: Starting execution of 25 actions...
[[14:59:14]] [WARNING] Failed to clear screenshots: Error clearing screenshots: No module named 'app.utils'; 'app' is not a package
[[14:59:14]] [INFO] Clearing screenshots from database before execution...
[[14:59:14]] [SUCCESS] All screenshots deleted successfully
[[14:59:14]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[14:59:14]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250614_145914/screenshots
[[14:59:14]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250614_145914
[[14:59:14]] [SUCCESS] Report directory initialized successfully
[[14:59:14]] [INFO] Initializing report directory and screenshots folder...
[[14:59:11]] [SUCCESS] All screenshots deleted successfully
[[14:59:11]] [INFO] All actions cleared
[[14:59:11]] [INFO] Cleaning up screenshots...
[[14:59:04]] [SUCCESS] Screenshot refreshed successfully
[[14:59:03]] [SUCCESS] Screenshot refreshed
[[14:59:03]] [INFO] Refreshing screenshot...
[[14:59:02]] [SUCCESS] Connected to device: 00008030-00020C123E60402E with AirTest support
[[14:59:02]] [INFO] Device info updated: 00008030-00020C123E60402E
[[14:58:59]] [INFO] Connecting to device: 00008030-00020C123E60402E (Platform: iOS)...
[[14:58:55]] [SUCCESS] Found 2 device(s)
[[14:58:54]] [INFO] Refreshing device list...
